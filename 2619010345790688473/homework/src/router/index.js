import { createRouter, createWebHistory } from 'vue-router';
import Login from '../views/Login.vue';
import FoodList from '../views/FoodList.vue';
import Me from '../views/Me.vue';

const routes = [
  {
    path: '/',
    name: 'Login',
    component: Login
  },
  {
    path: '/food-list',
    name: 'FoodList',
    component: FoodList
  },
  {
    path: '/me',
    name: 'Me',
    component: Me
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;
