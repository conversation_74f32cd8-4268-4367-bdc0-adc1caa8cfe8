<template>
  <div id="app">
    <router-view></router-view>
    <div class="nav-buttons">

		<div class='icon-container'>
			<img src="./asserts/home.png" alt="">
			  <el-button @click="goToPage('Login')" icon="">首页</el-button>
		</div>

		<div class='icon-container'>
			<img src="./asserts/shopicon.png" alt="">
			  <el-button @click="goToPage('FoodList')">购物</el-button>
		</div>

	<div class='icon-container'>
			<img src="./asserts/touxiang.png" alt="">
			  <el-button @click="goToPage('Me')">我的</el-button>
		</div>

    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router';
export default {
  setup() {
    const router = useRouter();

    const goToPage = (name) => {
      router.push({ name });
    };

    return {
      goToPage
    };
  }
};
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}

.nav-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 10px;
  background-color: #f0f0f0;
}

@media (max-width: 768px) {
  .login-container {
    width: 90%;
    margin: 50px auto;
  }

  .food-list-container,
  .me-container {
    padding: 10px;
  }

  .nav-buttons {
    padding: 5px;
  }
}

.icon-container {
  display: flex;
  flex-direction: column;  /* 垂直排列图标和文字 */
  align: center;
  height: 100%;
}
.icon-container img {
	width: 30px;
	height: 30x;
	margin: auto;
}
</style>
