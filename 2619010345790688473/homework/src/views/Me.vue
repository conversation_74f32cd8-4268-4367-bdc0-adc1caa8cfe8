<template>
  <div class="me-container">
    <div class="header">
      <div class="header-text">
        <span>web前端开发大作页</span>
        <span>2023级计算机237班张富, 学号: **********</span>
      </div>
    </div>
    <div class="user-info">
      <div class="avatar-container">
        <img
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMwAAADACAMAAAB/Pny7AAABRFBMVEX/////9qIAAABQFBT/qaf4eXa7u7tTFRX/+aS+vr64uLhVFRX4+Pj8/PzCwsL//6nT09PNzc3i4uLw8PD/ravq6ura2tqrq6t2dnb/paOIiIiYmJhFERGSkpL4b3OhoaHSzodfX182Njb5iXt+fn7/trRtbW0qKioXAAA8Dw81DQ35kX4AAA3/8KBJSUn/4+P/8fFwbUYXFxdVVVUkCQni3JL/ysn6nYL/nZufhRvu6JkhISEpAAA/Pz+JWllxS0r7son6qIX+5Zv91Zb8yJGinmizr3MkIhCAgFWPdxhEOABANQtZSg+DbRYuJQBTNjaYSknqn53Hh4VGRSxXVTcyMSAUEweRjl4UFBwgFQByYBNiUACMf1iRf0QkJzG4mSDQrSQAAh0zJCRYKypwNzXKY2GyWVdFIyOncXDhb2yOUE/OfnxkTKc2AAAV/0lEQVR4nO2d+VfaarfHdVsDCUkgTKEBiiAy1WIFqlVAbY/z3DrWc297Xjtoz/3/f7/7SQJkhBACut7V71mrR7ssPB/2+Exxavq/SFNPPQAv9QfmueoPzHPVH5jnqj8wz1V/YJ6r/sA8V/2B8VD+YDDol//w4MWeFiYYnNu52d/fv9nZXfeA5ylh/MG55QtQtHe5vPNuVJ4nhAmu719BMxOPxWIhMb8IcHW5i7Ya4RWfDMYf3L0CoDluShbHxpJFgOv9dyNY56lgguvLAOkpvWJp5Llwj/M0MBgte5ANqwwc17HP1BRTvYXldb87nCeB8ft3oZZkleG/XVpZWFhZevtWBWLqi3u77mieAsbv34Eco5piaeH9S9T7FysIpPxVMueS5glggshSFVQPW3rx8oWily+jK0uKdfiqO5rJw/j9N1APKSzsSgdF4YmuKMbh21dzTwaD7YjT+hBcRxY19N8u6FgIzsKSbDCqeDE3fMXxAib4Zntr6y9nfoGVsg8L0rxYkWnEW0zRww5kdBh/cHt+FvXhjQMaP7IUYvYsRApNBq6Gts3IMME3r2dlvdoe/MOEJR/pz6LSTIlwvTNk+RwVJvhx/pUK83qwabC+JAbYhbiaQiMM3Q2MCOP/+FplQX0c9Mb+d1dFwcQifxGN9lhevlhScl19EW6G6QZGgPGj3qz2WF4NgiFOJupYcNzRKNb/lSXsAFBLS6QdWIguqOVTrMLVrnMclzDIMf3m45s3Wz2WgTAkkUlqwKy8R5CXUbmPmTKKfdv9y1i6Che776adeZsrGL//41/bWx9WVz/M91hm5/vCkJkYJNSkvPQeQVasQEyKJxFnZ84RjhsY/5vt1dlXqFmtXn3o92+C07sXkO4WGGcgikJiHa5unOC4gPF/RBQy/NXXOsv8Zf9maJZ9AEb1MWyUub7DN1mHQZz93YE4w8P435Bk/Gp1dnbrgwbmVZ+iGVzfuYBCaCgAvcJ8AfYud9b747iA+UDMsoowH1bnV3uG2bZ7n2AQPey2axaXioQSWHdu/P1whod5gyzzqx+2yJ9oo3mlAXi1ZfMm/uC7SwApNhoKEReTAIOnj7MNDeP/S4aZ/4AQr+bnX63KnvbKLpUFpxGl01mOLDYJcH1j62zDw2zLEbOlRMv8/KximW2rptAfnL4BqPIeochKNsG2LXBnGaRAiNfkfwrMlgWL37++uwfVjJcoKDbdBtixxHERMx2TrCowciYzvzRBuYBqmvWYZarTFqybPz8XdWabVJnXqE5etmDBsN9dhnY67j0KUTxdhP0507u6KZp/fZArjdwEkD5ga9rwqv7gOqIU08JwtXEY8RJpQQ3GcdfOfNzG1oxoa2t72zRhDvp3yXKlMAYP6ynCtNE4+rzmrmsmw38jS+6fTSjXkI+PFYUonsfI0b37iPMZ018GsTneg0J8xHrvSKy4uHejHYO362bYUF5eQz0+vljRS2jDpcbTvIRROpfqKA3lsIrUAXppwDMY9DlEWeysu05MeYDuAptHMOi5OJG8rdMTRkEla9CpOJ7AYF3ByddtVpw8CkrMXe8qlc4DGIJyA4sFcezJ2EZ8lbSeXsCQao9T4sSok69RFMoqNCOvaE7v7u9Bgn9CFFS8APtIMxoMqfZYIgUP5pGjKVyA5Wn/KDCIcokooae1iqJYAcunaxh/MLh7dY2Ny1OFvUFsFi7dLs+SjUmAgleTey+UBVdTACz2mMCK+ScPFb2qLtbNptex2C9WpUl1k47FDQnjD06/272EYlZ8DlFv0nAo63M7F5DLU8/OKoqcowT966RA1pPjnQ+PIucoczs4HU4wY1pw8UQO/Ss4t38BtxL/zBKYQY5Q/HOXV1AUw8/Wv1QN9i+yu7IHbZ59plGv0WD/WsZSn32WmdikPiB+f/Dd/jUsFtPP3yaK7EmwpuzBbbvg6Y7EeGWNMi1Xx8V6Qnze6csgS6vM7WL3Vc+Lz6kpdiKL9PWOtCzSs66ONjKzYCPZzoQ0JUVzUveZy8yyB5nOYjG3tLJCTuY4Pk3xxDKxXIFqlbeIESUnj94PeaDi6WSI/fVlUBa+38og8jmqpSceonMZDXOdl82w1DvbNnEW9yFqgLkBUli4lfdPwxIJCRRDRPHxyPBMRi+7JbVlqcuinv6ciLiYwIhiJi3lJSkjiiIfG7ZL18O8u6yjZbjugcmXE2SJIIpUx672y5dT/LNdSIr0kJMOIwzpj992DbMwuRlMmM8kvsLx3d3dycnByd3d33tQlUR+KByjm+lhJldg4ozUhruDg7WDk7+P/+d/Tw4OTk4OoZ6khln9NVhmuapxs/eTc7I4nb89RJAjsk7Kx4UkGgktBEWJ4Z33VUbLtAmMeug4OrFiGePzi4ffTkisKBNBTsKvjk7WDm/TFON4n9QIk+vBvJ9YVuZ4Bk6//a3cclTmT3yOfH2HNBnnNJYwyrWWyRkmzGdh7US9sqnEiCDDYBR9qVMU43AuYulmMszkDDMl0LXrf1QWUMyQVr87uIMkTTncxbaFeTm5XpkTkot3Bx2YJnEIKqd+d3RylEUYZxNeKxi5A5igYaYEafHbUQcGahkm0f0GDo6bNEU5y2h6mOn9rwSGFJpJTmLi6cWDQ7DWwTEgjLMUMK3XPpBYe0samQlOYiLi4vGdDQyWTpcw/hsFZuFldGJeFonzYh7g2BYmhzDOThfpYXAKQP4ZTgFWJuJlbIhJ5utFMujbYi5XrJlYDu++5BHGRQKYDu6AnAWXJtHJsKF0oorDr+eTNC8IIZTA80iX67F8uTtcFDGbOfN5A8zunkj+3dLC2L0snMy2EUQU4rGIrjNmI+GQWFBYro+PIe+yaE775y7kaTP3dszhHyrgx59lYjbTSS4SE4nLnR7jD9GM0/N4BhicnU1gCkN6lVpm0LyYIbbJUjzjeLXbALO+DGPfvYjjZ+7sxGAGafKO7WJeNxs3DBfOQjPrtA2OFWpQiDl2eTPMWNf9Yxmo1YfYJGEzTSiIfNzZ2obRzS5vxxgzLF3H/DVcbmHkRF3PODkHZjDM3FV+fDCxNDQlc5ZlI323S2msqYUsQIIaODJT0RzfRplQgDqvH1CYziTJOlk6Kdqf7OYBpDCTr0F+UCrQe5naaI5FYhvyuhePiVK2W+xr9bzteRwGauJUBD+L6oDzucYyUx1X/CeboBttWKo3AVrllKJKCZOcnVcgRpz01lAzPqpGL0PI7EljChkJQOtIEQlt0krNoHxE+P9UA5o2ySFSA2mKpHXzg3d00sPsjitkkEVrcgbrZmWGIPTk85UBbPxIgLZcmliAZJ930YfMDYzlWgKbh5zW4lW0igGFKJAqgXU5ZbOyaQhNkbJ/H0PIeHalUjcUCeqacEFnaaQCJhSZBsDazYXO7Q8BsvZRrYOZu5LG0MxwSe21U05AswQ0Zik3KqkuTQWsr0LG6qoHssk+jmYImXHcTRCh3ltc4ZgmVDRm8aFrQalLMwNg+RpspvMkkVi1artUo0sAYymZNOQ0gcDnoKx1MV+KVJlG1zQNsB4qDznFzzgRMnb1VdeYLbe9j/94scn0vhPaUNZFvgIDA/0sXu980KF21i6wdfF/Mfqpay4Wi0W0064qJHvfYf9fNgQ9lhdUqseWt3zdiARqEWLTtilXF/8wasmMhQRFoU7KyUO295oYvS1TCqtoYWZSkLV8ZS4DnavFDNg13tqQ2bUxsVNFBCGTqOZy9UKS50PyG9KgnezFMTjM5QVpSr4eTN069YqQUF9JyOVtcq7WMiMms5CQLN4uyiGwmBN5cmKYq4NmAT9SMARMx7V6roeVJmvtREz3IU/hbNum1Gjr/35uhGTGCgwW9qNv/xAdYtfOCxxxMi0ttFSWVMoCSk0AkvXNIqpbLSOJpk1oa5PZZd39sayIgBPc02/yYv5//rO29uVWosKYh7Wno7IdwwBYxI4CUwKRtxwqXev4HzZHDmAuCq7rf0RI5k5P/iGbkjmJydSPvp3mMnxW57cRtZwE5PxlQ1OqCbTlUJluMHGSExhIuE1mrJBpn66tkUEqi0jVwzUoSLW29tNh1NKvwFiGD3pZgqcshyp2Y8aRZd6pnenw4uJi/cuavPdVUxJiHO6OizlgtDm0E/5qnaxYGgYY2nKbDLN6J4fFCnYzSB1M/3mcvSJUHg7W9sgQ1RzCwRGy6RvcZqea+Bo2MGiYLE/xVv4RyYP65E2OytmFg65m9pv49FMo8/XvNWUfr6j0LgKc3p0aMj2UuqNukObS5GbYdNZEmrJMqeECKK/MhdMdrH4wu4suy0yEQcN0dr7lpapIG66PTg3JUVMaMTenjChYThsg0ZRlyLB8pzeL8HnbdlgDs3PL2PzQAMXFr3eKk5GgSQhhhhxOutY2ZQqM5YyspwrUGYSxegs2k1O6ZpYX683BvZn/5qvLmkknobfzDdBsKhtgTcOwimC2hs7JoJihKet18lhafZogzfcyQT+Y5ZzLy/xM4mjNYkvS2ILn9TMZM0styVOUpXdwQkKZxVAM0wmeATBVdxMAjqker5lZTLkxBA2bJkZlkQiLZWyzYruGTR5HM3SmW2/6wQQvXcJEmEVLGGNu5Npg15L5MF6KMot1QxVKEjtHeIbGBtDJGkDwwqb5HgwDVjBNkzfQljMAgpJqQTtNWKxDm0XfSrJhmqH4BGZ/29DWwFy5vPJjDVPMmF6NTVh2ZGiWEmQzhMXmGUJxsZYTBXKmVoJGBWyXkDQxs5d115qxVm7Wtvr4YlloGDzNF5gpN8jVT9ku1gPgsMHIMhRFI0spNdMoOkjNUHDZZzI5E4zNchDW8VIl1Vk18yFJqtKAWiHDY062Y5kKUc0iJjqeStRI1FWcFE0ouGOZ4gunJ0d6u9ilkhCOp9Qop8hy+UyqXGmUCApDzEKFbWb2EQyULE3zYhYIi6/sYA3APYygL5rYBdimRSzg8vGrEhH5IieJ2Ccji/2TKwQGmhmBSeSwuUObBsq2C2damIRLmJjYPtRVzT6rb2H0p0yBnDKBZjubRhAkoRjRziz4L4QqTnISxW6f3XJkGbcwU1QSTtZOuyy2K45EccxJPVFETL8TpRFBQvsBdNI6mQ3ZnXLsway7hwljHThZOyS95vU1DKi9YRIfXTEUQ4f6wHMCQw49lVoznbRRqdnse3gEw9JMFtPzwfHR4fER9NlAUX5akHMXuYuBRhLsHWyKzGGpOpaWVKDb1ZXv720/rZ6XjQCDnzaJz9OTk5Pj7rOy+w0xHCdLn6FQeMD5GWRJ1Eozmv40dX8Gtk9N7MG8s1nldSJWoPhMot5ut2138lwpTKeL+oaucXZmb3pvYOQekGfETBJGWHszio3zmZxupd3XOtvo80wVj2DI7RdMs9hvuF3hsXpJSmzr1j0CyHLWx/ReweDHiDhM1dwru1WcJyzazjRQud/Y7DOd0cDMjQYzxbGRSMSuKRtarIBzl1xvS01mKW1slIp9Pi3vYIhCrlsig8I8w9MA339UfBq7bG7c9025WpjR3T3pTcigWSherMGP2Z/lTi4jPoYBU+2Xy72FqdY8eAgwG8dcwicBzmdnZ391EoDCkutbxLyF6Xt+wpE4NoydAU0loPFLfkzveUXDUuwfkR7DuF2tUsVFyESfpsU6PPxUHzp8XiYT6zPZxwZshnsM0x4lmUVicZrMjhkpBz9+dx8Ff95KEZbN+4EHe54NDBuOC5Q80c9koXLeY8G4aaCPbZw1bFv/jp6Hm7Fh+VIzQRELaJafszr9/HF29pBKDcz7Whi32zM9Qb+SZiM2Fif3GcgcB9u7fBW+G1BQv3/+2yiVSzDg4K3Hlqk53OLhSL8QDqNrKZMbMk9DogT2Yj9VD/v96/zHzPcfqPfz8/O/f//8MRTM6B1A1eFyFU6Xlck/1Z128hgrTaW2oF+dlx82Hj9/UqTeTY5u2hxGs4QZ3TJ5Z72ZoJk3U7J7CUyezI4VlPPG5udP0ajmtwUpt8aHghndMgK0HTx2p8tCK2IyZO2l9KCgzD48yg/vMAkt49jN3G8298RCLW+3LtkRJ7MwisSMVGiTpZfv3Qx29nnB8vdSRT+dDQMzumW4AmRtV1kVkSaSZhKFQiFbz5ElJKh8/3HeS2A/rFiIz0U/39cHvL0Wxv2CRlcCFJP9lo64GE/WZJRLpaXGA3Kc/9QVyE0zSzT66fPGxuPZwENXPZhRVme6itShwDC2DyRg5X6Fz0OpUqnA+S8dB2bj3782P5nCJfrp8ayFKtmceLSAGWXdTCMGmkmcWFk/kjYmNyy8BFAO+AKtcz3J+b//Pj5asHzebKXwx2dK7UH7R17DxMj1A5ph+JDp6RdsiFc2WZrKKoWvpPevz1hXjOkYWR4fymQx01exXWIeG8wUX4QsRYo6Fg/tIl8kxCt9ZKKmrrj4tKYh/mWqLAqLPNf0pUrNgSWsB+N+S0OvDHkKhrIerpQSnuexZ6HlUs8zWUxfnc0m6LWSmxYgso89qD9rv5ExThguD5AVKCvRPPllxt3TAAHoZOTfDZva8lndNvSlwMExZR2MNzdN2QLg9LnTevVI5AkkgOYaQEr1s9/3lmbpsZCjQw768R5M8NrlBq1JEVJG8kyvl5S/YnDSBfrNc9+9AvPdnMOUnHzWOWFXtj9jYgfj2a2GNJb2Zj4jykAkG4iZvNy1lHUHAQJK0Jx/NrJEX5B08GmjEegEjKNf0aGBuepzmWNY0QXSBLezCSmdlqSCfLesZEAhx0tJcv5lLC7YuTziBODzhnqmK5ByuOqjgXF7QsNSLCVltXu2JXKxxHg8I9DADPD7X4Nh0CIPjUqr1Gp1WRzuk2hgLr1aJ1bExeksNBqlUqPVqsib5TNGyZb59Whk2azMBDo30ZT7Tg4vqmqymfd3NCRIzaTUW3ImkhkffuToZf+ndzJN1Cs/5ZzFk/NmtkqTHsz6IBP2WpXS/cPs7PsXesMsnOl2ygLlkv4MrkMY1ycBbRWvgiUJelG5VDrb3Pg+O6uP/OjCZkN774ncenA+Kg3MztdRF4pNwl4/YDr9FyBRcL+5gXqYfd+1S3Rh4dPnx/uWjqUEgzsySxjXp2f7KAGmA3PlhkqysXkG89GOTXDSItdULTuaJTFM7dPAuD/X3I+mqb0el6rcN886JKVSChTDIEmDXHcMaG4J+mbQfrnhdkh0MKMvaZrFkILZKpcrrcb9/Rkh2dzcPLtvNMq+QKDyuCDXyAf0Rp07yocDi/kha4UGxovlGStR+YK8cAH3Dw8P9/fyYSZAFDLoe9K2PD7M6ANLRqklhl7q1cCsw7gebBDjM2kpUUdV69lCPp1JtsmRQPSp1AN2LQ86EHIOrYVzIhe/A0oLc+Fhc2YSG4vHQ/hfPEZCWiiQS+foaKnKQ1kDEvClsJEhzzV0MxQtzLKHpysGKSZWMXdVylhyAqo6RwOhmnH524amNbrxvAXoIy7GF+QGtNGqELUaDTmakKTvOad+0sLsLHp2vMKRIhGm0NS21tk0b3hw03DSwox6T9OFOI6LhHiGYehQjB318eP/D4ErzL9uBLX7AAAAAElFTkSuQmCC"
          alt="avatar"
          class="avatar-image"
        />
      </div>
      <div class="user-details">
        <div class="user-name">用户名称:小陈每天都要笑</div>
        <div class="user-account">用户账号:**********</div>
        <el-button type="text" class="status-btn">+状态</el-button>
      </div>
      <div class="qr-code">
        <img src="../asserts/erweima.png" alt="二维码" class="qr-image" />
      </div>
    </div>
    <div class="settings">
      <div class="setting-item" v-for="(item, index) in 5" :key="index">
        <span>设置</span>
        <el-icon><ArrowRight /></el-icon>
      </div>
    </div>
  </div>
</template>

<script>
import { Setting, ArrowRight } from '@element-plus/icons-vue';

export default {
  components: {
    Setting,
    ArrowRight
  }
};
</script>

<style scoped>
.me-container {
  padding: 0;
  width: 800px;
  height: 900px;
  margin: auto;
  background-color: #f5f5f5;
}

.header {
  background-color: #ff9933;
  color: white;
  padding: 15px 20px;
  text-align: center;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.header-text span:first-child {
  font-size: 18px;
  font-weight: bold;
}

.header-text span:last-child {
  font-size: 14px;
  opacity: 0.9;
}

.user-info {
  display: flex;
  align-items: center;
  margin: 0;
  background-color: #e8f4fd;
  padding: 30px 20px;
  position: relative;
}

.avatar-container {
  margin-right: 20px;
}

.avatar-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.user-account {
  font-size: 14px;
  color: #666;
}

.status-btn {
  align-self: flex-start;
  margin-top: 5px;
  padding: 4px 12px;
  border: 1px solid #ddd;
  border-radius: 15px;
  font-size: 12px;
  color: #666;
}

.qr-code {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.qr-image {
  width: 40px;
  height: 40px;
}

.settings {
  margin-top: 20px;
  padding: 0 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
  background-color: white;
  margin-bottom: 1px;
}
</style>
