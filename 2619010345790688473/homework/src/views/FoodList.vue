<template>
  <Banner />
  ¦ <div class="banner-text">
	¦   <h2>多彩欧洲美味 纵享万千搭配</h2>
¦  </div>

  <div class="food-list-container">

  <h3>主题系列</h3>
    <div class="food-categories">
      <div class="category-item" v-for="(category, index) in categories" :key="index">
        <img :src="category.image" alt="category" style="width: 50px; height: 50px;" />
        <span>{{ category.name }}</span>
      </div>
    </div>
    <div class="featured-dishes">
      <h3>招牌菜系</h3>
	  <div class="cai" v-for="(food, index) in foods" :key="index">
		  <div class="cai-raw">
			  <div class="cai-columns">
				  <img :src="food.image">
				  <div class='price'>
					  <div >{{food.name}}</div>
					  <div >{{food.price}}</div>
				</div>
			  </div>
			  <div class='count'>
				<button @click="food.count++">+</button>
				<span>{{food.count}}</span>
				<button  @click="food.count--">-</button>
			  </div>
		  </div>
	  </div>
    </div>
  </div>
</template>

<script>

import Banner from '../components/Banners.vue';
import {ref} from 'vue';
export default {
  components: {
		Banner // 注册组件
  },

  data() {
    return {
      categories: [
        { name: '湘菜系列', image: '/src/asserts/xiangcai.png' },
        { name: '川系列', image: '/src/asserts/chuancai.png' },
        { name: '粤菜系列', image: '/src/asserts/yuecai.png' },
        { name: '鲁菜系列', image: '/src/asserts/lucai.png' }
      ],
		banners: [
			{image:"/src/asserts/banner1.png"},
			{image:"/src/asserts/banner2.png"},
			{image:"/src/asserts/banner3.png"},
			{image:"/src/asserts/banner4.png"},
		],
		foods: [
			{name:"毛氏红烧肉", image:"/src/asserts/lucai.png", price:"￥20元", count: ref(0)},
			{name:"佛跳墙", image:"/src/asserts/yuecai.png", price:"￥40元", count: ref(0)},
			{name:"麻婆豆腐", image:"/src/asserts/chuancai.png", price:"￥33元", count: ref(0)},
			{name:"九转大肠", image:"/src/asserts/xiangcai.png", price:"￥20元", count: ref(0)},
		]

    };
  }
};
</script>

<style scoped>

div {
	border: none;
}
.food-list-container {
  padding: 20px;
  width: 800px;
  margin: auto;
  background-color: #e6f0f5;
  margin-bottom: 50px;
}

.banner {
  position: relative;
}

.banner-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  text-align: center;
}

.food-categories {
  margin-top: 20px;
  display: flex;
  justify-content: space-around;
}

.category-item {
  display: inline-block;
  margin: 10px;
  text-align: center;
}

.featured-dishes {
  margin-top: 20px;
}

.dish-item {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-top: 10px;
}

.cai {
	display: flex;
	flex-flow: column, nowrap;	
	align-items: space-between;
}

.cai img {
	width: 100px;
	height: 100px;
}
.cai-raw {
	display: flex;
	width: 100%;
	flex-flow: row, nowrap;	
	justify-content:space-between; 
	margin: 10px;
}
.cai-columns{
	display: flex;
	width: 100%;
	flex-flow: row, nowrap;	
	margin: 10px;

}
.dish-item {
	margin: auto;
}
.count {
	display: flex;
	align-items: flex-end;
}

.count button, span {
	margin: 5px;
}
h3 {
	text-align: left;
}

.price {
	display: flex;
	flex-direction: column ;
	align-items:flex-start;
	margin: 10px 20px;
}

.price div {
	margin: 10px;
}
</style>
